import { useState, useRef } from "react";
import { invoke } from "@tauri-apps/api/core";
import { Upload, Image, Loader2, Download, Settings } from "lucide-react";
import "./App.css";

interface TaskResult {
  output: {
    task_id: string;
    task_status: string;
    results?: Array<{
      url?: string;
      code?: string;
      message?: string;
    }>;
    submit_time?: string;
    scheduled_time?: string;
    end_time?: string;
  };
  request_id: string;
  usage?: {
    image_count: number;
  };
}

function App() {
  const [selectedFile, setSelectedFile] = useState<string>("");
  const [apiKey, setApiKey] = useState<string>("");
  const [modelName, setModelName] = useState<string>("wanx2.1-imageedit");
  const [editFunction, setEditFunction] = useState<string>("stylization_all");
  const [prompt, setPrompt] = useState<string>("");
  const [maskImagePath, setMaskImagePath] = useState<string>("");
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [uploadedUrl, setUploadedUrl] = useState<string>("");
  const [taskId, setTaskId] = useState<string>("");
  const [resultImages, setResultImages] = useState<string[]>([]);
  const [error, setError] = useState<string>("");
  const [progress, setProgress] = useState<string>("");

  const fileInputRef = useRef<HTMLInputElement>(null);
  const maskFileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleMaskFileSelect = () => {
    maskFileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // 暂时使用文件名，实际使用时需要用户手动输入完整路径
      setSelectedFile(file.name);
      setError("");
    }
  };

  const handleMaskFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setMaskImagePath(file.name);
    }
  };

  const uploadFile = async () => {
    if (!selectedFile || !apiKey) {
      setError("请选择文件并输入API Key");
      return;
    }

    setIsUploading(true);
    setError("");
    setProgress("正在上传文件到阿里云...");

    try {
      const url = await invoke<string>("upload_file_and_get_url", {
        apiKey,
        modelName,
        filePath: selectedFile,
      });
      setUploadedUrl(url);
      setProgress("文件上传成功！");
      setTimeout(() => setProgress(""), 2000);
    } catch (err) {
      setError(`上传失败: ${err}`);
    } finally {
      setIsUploading(false);
    }
  };

  const startImageEdit = async () => {
    if (!uploadedUrl || !prompt) {
      setError("请先上传文件并输入编辑提示词");
      return;
    }

    setIsProcessing(true);
    setError("");
    setProgress("正在创建图像编辑任务...");

    try {
      const parameters = { n: 1 };
      const taskIdResult = await invoke<string>("create_image_edit", {
        apiKey,
        imageUrl: uploadedUrl,
        function: editFunction,
        prompt,
        maskImageUrl: maskImagePath || null,
        parameters,
      });

      setTaskId(taskIdResult);
      setProgress("任务已创建，正在处理中...");

      // 开始轮询任务状态
      pollTaskResult(taskIdResult);
    } catch (err) {
      setError(`创建任务失败: ${err}`);
      setIsProcessing(false);
    }
  };

  const pollTaskResult = async (taskId: string) => {
    const maxAttempts = 60; // 最多轮询60次，每次间隔5秒
    let attempts = 0;

    const poll = async () => {
      try {
        const result = await invoke<TaskResult>("get_image_edit_task_result", {
          apiKey,
          taskId,
        });

        const status = result.output.task_status;

        if (status === "SUCCEEDED") {
          const urls = result.output.results
            ?.filter(r => r.url)
            .map(r => r.url!) || [];

          setResultImages(urls);
          setProgress("图像编辑完成！");
          setIsProcessing(false);
          setTimeout(() => setProgress(""), 3000);
        } else if (status === "FAILED") {
          const errorMsg = result.output.results?.[0]?.message || "任务执行失败";
          setError(`任务失败: ${errorMsg}`);
          setIsProcessing(false);
        } else if (status === "PENDING" || status === "RUNNING") {
          attempts++;
          if (attempts < maxAttempts) {
            setProgress(`任务处理中... (${attempts}/${maxAttempts})`);
            setTimeout(poll, 5000); // 5秒后再次轮询
          } else {
            setError("任务处理超时，请稍后手动查询");
            setIsProcessing(false);
          }
        }
      } catch (err) {
        setError(`查询任务状态失败: ${err}`);
        setIsProcessing(false);
      }
    };

    poll();
  };

  const downloadImage = async (url: string) => {
    try {
      // 在浏览器中打开图片URL
      window.open(url, '_blank');
    } catch (err) {
      setError(`打开图片失败: ${err}`);
    }
  };

  return (
    <main className="container">
      <h1>AI 图像编辑工具</h1>
      <p>基于阿里云通义万相的图像编辑功能</p>

      {error && (
        <div className="error-message">
          <p>错误: {error}</p>
        </div>
      )}

      {progress && (
        <div className="progress-message">
          <Loader2 className="spinner" />
          <p>{progress}</p>
        </div>
      )}

      <div className="config-section">
        <h2><Settings size={20} /> 配置</h2>
        <div className="form-group">
          <label>阿里云API Key:</label>
          <input
            type="password"
            value={apiKey}
            onChange={(e) => setApiKey(e.target.value)}
            placeholder="请输入您的阿里云百炼API Key"
            className="api-key-input"
          />
        </div>

        <div className="form-group">
          <label>编辑功能:</label>
          <select
            value={editFunction}
            onChange={(e) => setEditFunction(e.target.value)}
            className="function-select"
          >
            <option value="stylization_all">全局风格化</option>
            <option value="stylization_local">局部风格化</option>
            <option value="description_edit">指令编辑</option>
            <option value="description_edit_with_mask">局部重绘</option>
            <option value="remove_watermark">去文字水印</option>
            <option value="expand">扩图</option>
            <option value="super_resolution">图像超分</option>
            <option value="colorization">图像上色</option>
            <option value="doodle">线稿生图</option>
            <option value="control_cartoon_feature">参考卡通形象生图</option>
          </select>
        </div>

        <div className="form-group">
          <label>编辑提示词:</label>
          <textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="请输入图像编辑的提示词，例如：转换成法国绘本风格"
            className="prompt-textarea"
            rows={3}
          />
        </div>
      </div>

      <div className="upload-section">
        <h2><Upload size={20} /> 文件上传</h2>

        <div className="file-upload-area">
          <button
            onClick={handleFileSelect}
            className="file-select-btn"
            disabled={isUploading}
          >
            <Image size={20} />
            选择图片文件
          </button>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="hidden-input"
          />
          {selectedFile && (
            <p className="selected-file">已选择: {selectedFile.split('/').pop()}</p>
          )}
        </div>

        {editFunction === "description_edit_with_mask" && (
          <div className="file-upload-area">
            <button
              onClick={handleMaskFileSelect}
              className="file-select-btn secondary"
              disabled={isUploading}
            >
              <Image size={20} />
              选择遮罩图片 (局部重绘需要)
            </button>
            <input
              ref={maskFileInputRef}
              type="file"
              accept="image/*"
              onChange={handleMaskFileChange}
              className="hidden-input"
            />
            {maskImagePath && (
              <p className="selected-file">遮罩图片: {maskImagePath.split('/').pop()}</p>
            )}
          </div>
        )}

        <button
          onClick={uploadFile}
          disabled={!selectedFile || !apiKey || isUploading}
          className="upload-btn"
        >
          {isUploading ? <Loader2 className="spinner" /> : <Upload size={20} />}
          {isUploading ? "上传中..." : "上传到阿里云"}
        </button>

        {uploadedUrl && (
          <div className="upload-success">
            <p>✅ 文件上传成功！URL: {uploadedUrl}</p>
          </div>
        )}
      </div>

      <div className="process-section">
        <h2><Settings size={20} /> 图像处理</h2>
        <button
          onClick={startImageEdit}
          disabled={!uploadedUrl || !prompt || isProcessing}
          className="process-btn"
        >
          {isProcessing ? <Loader2 className="spinner" /> : <Image size={20} />}
          {isProcessing ? "处理中..." : "开始图像编辑"}
        </button>

        {taskId && (
          <div className="task-info">
            <p>任务ID: {taskId}</p>
          </div>
        )}
      </div>

      {resultImages.length > 0 && (
        <div className="results-section">
          <h2><Image size={20} /> 编辑结果</h2>
          <div className="result-images">
            {resultImages.map((url, index) => (
              <div key={index} className="result-item">
                <img src={url} alt={`编辑结果 ${index + 1}`} className="result-image" />
                <button
                  onClick={() => downloadImage(url, index)}
                  className="download-btn"
                >
                  <Download size={16} />
                  查看原图
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </main>
  );
}

export default App;
